import { z } from 'zod';

/**
 * Report Types
 */
export const reportTypeSchema = z.enum([
  'AMC',
  'WARRANTY', 
  'SERVICE',
  'SALES',
  'CUSTOMER',
  'FINANCIAL',
  'ANALYTICS'
], {
  errorMap: () => ({ message: 'Invalid report type' })
});

/**
 * Report Formats
 */
export const reportFormatSchema = z.enum([
  'JSON',
  'CSV',
  'EXCEL',
  'PDF'
], {
  errorMap: () => ({ message: 'Invalid report format' })
});

/**
 * Date Range Schema
 */
export const dateRangeSchema = z.object({
  startDate: z.coerce.date({ message: 'Valid start date is required' }),
  endDate: z.coerce.date({ message: 'Valid end date is required' }),
}).refine(
  (data) => data.endDate >= data.startDate,
  {
    message: 'End date must be after or equal to start date',
    path: ['endDate'],
  }
);

/**
 * Base Report Filter Schema
 */
const baseReportFilterObject = z.object({
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
  customerId: z.string().uuid().optional(),
  executiveId: z.string().uuid().optional(),
  status: z.string().optional(),
  search: z.string().optional(),
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(1000).default(100),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export const baseReportFilterSchema = baseReportFilterObject.refine(
  (data) => {
    if (data.startDate && data.endDate) {
      return data.endDate >= data.startDate;
    }
    return true;
  },
  {
    message: 'End date must be after or equal to start date',
    path: ['endDate'],
  }
);

/**
 * AMC Report Filter Schema
 */
export const amcReportFilterSchema = baseReportFilterObject.extend({
  amcStatus: z.enum(['ACTIVE', 'EXPIRED', 'CANCELLED', 'PENDING']).optional(),
  contractType: z.string().optional(),
  amountMin: z.coerce.number().min(0).optional(),
  amountMax: z.coerce.number().min(0).optional(),
  serviceType: z.string().optional(),
}).refine(
  (data) => {
    if (data.startDate && data.endDate) {
      return data.endDate >= data.startDate;
    }
    return true;
  },
  {
    message: 'End date must be after or equal to start date',
    path: ['endDate'],
  }
).refine(
  (data) => {
    if (data.amountMin && data.amountMax) {
      return data.amountMax >= data.amountMin;
    }
    return true;
  },
  {
    message: 'Maximum amount must be greater than or equal to minimum amount',
    path: ['amountMax'],
  }
);

/**
 * Warranty Report Filter Schema
 */
export const warrantyReportFilterSchema = baseReportFilterObject.extend({
  warrantyType: z.enum(['IN_WARRANTY', 'OUT_WARRANTY']).optional(),
  warrantyStatus: z.enum(['ACTIVE', 'EXPIRED', 'CLAIMED', 'VOID']).optional(),
  productId: z.string().uuid().optional(),
  modelId: z.string().uuid().optional(),
}).refine(
  (data) => {
    if (data.startDate && data.endDate) {
      return data.endDate >= data.startDate;
    }
    return true;
  },
  {
    message: 'End date must be after or equal to start date',
    path: ['endDate'],
  }
);

/**
 * Service Report Filter Schema
 */
export const serviceReportFilterSchema = baseReportFilterObject.extend({
  serviceStatus: z.enum(['OPEN', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'PENDING']).optional(),
  complaintType: z.enum(['REPAIR', 'MAINTENANCE', 'INSTALLATION', 'INSPECTION', 'WARRANTY', 'OTHER']).optional(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
  technicianId: z.string().uuid().optional(),
}).refine(
  (data) => {
    if (data.startDate && data.endDate) {
      return data.endDate >= data.startDate;
    }
    return true;
  },
  {
    message: 'End date must be after or equal to start date',
    path: ['endDate'],
  }
);

/**
 * Sales Report Filter Schema
 */
export const salesReportFilterSchema = baseReportFilterObject.extend({
  salesStage: z.enum(['LEAD', 'OPPORTUNITY', 'QUOTATION', 'ORDER', 'CLOSED']).optional(),
  salesStatus: z.string().optional(),
  amountMin: z.coerce.number().min(0).optional(),
  amountMax: z.coerce.number().min(0).optional(),
  source: z.string().optional(),
}).refine(
  (data) => {
    if (data.startDate && data.endDate) {
      return data.endDate >= data.startDate;
    }
    return true;
  },
  {
    message: 'End date must be after or equal to start date',
    path: ['endDate'],
  }
).refine(
  (data) => {
    if (data.amountMin && data.amountMax) {
      return data.amountMax >= data.amountMin;
    }
    return true;
  },
  {
    message: 'Maximum amount must be greater than or equal to minimum amount',
    path: ['amountMax'],
  }
);

/**
 * Customer Report Filter Schema
 */
export const customerReportFilterSchema = baseReportFilterObject.extend({
  customerType: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  isActive: z.coerce.boolean().optional(),
}).refine(
  (data) => {
    if (data.startDate && data.endDate) {
      return data.endDate >= data.startDate;
    }
    return true;
  },
  {
    message: 'End date must be after or equal to start date',
    path: ['endDate'],
  }
);

/**
 * Financial Report Filter Schema
 */
export const financialReportFilterSchema = baseReportFilterObject.extend({
  reportSubType: z.enum(['REVENUE', 'PAYMENTS', 'OUTSTANDING', 'PROFIT_LOSS']).optional(),
  paymentMode: z.string().optional(),
  amountMin: z.coerce.number().min(0).optional(),
  amountMax: z.coerce.number().min(0).optional(),
}).refine(
  (data) => {
    if (data.startDate && data.endDate) {
      return data.endDate >= data.startDate;
    }
    return true;
  },
  {
    message: 'End date must be after or equal to start date',
    path: ['endDate'],
  }
).refine(
  (data) => {
    if (data.amountMin && data.amountMax) {
      return data.amountMax >= data.amountMin;
    }
    return true;
  },
  {
    message: 'Maximum amount must be greater than or equal to minimum amount',
    path: ['amountMax'],
  }
);

/**
 * Analytics Report Filter Schema
 */
export const analyticsReportFilterSchema = baseReportFilterObject.extend({
  analyticsType: z.enum(['TRENDS', 'PERFORMANCE', 'COMPARISON', 'FORECAST']).optional(),
  groupBy: z.enum(['DAY', 'WEEK', 'MONTH', 'QUARTER', 'YEAR']).optional(),
  metrics: z.array(z.string()).optional(),
}).refine(
  (data) => {
    if (data.startDate && data.endDate) {
      return data.endDate >= data.startDate;
    }
    return true;
  },
  {
    message: 'End date must be after or equal to start date',
    path: ['endDate'],
  }
);

/**
 * Report Export Schema
 */
export const reportExportSchema = z.object({
  reportType: reportTypeSchema,
  format: reportFormatSchema,
  filters: z.record(z.any()).optional(),
  includeHeaders: z.boolean().default(true),
  filename: z.string().optional(),
});

/**
 * Report Statistics Schema
 */
export const reportStatisticsSchema = z.object({
  reportType: reportTypeSchema,
  period: z.enum(['TODAY', 'WEEK', 'MONTH', 'QUARTER', 'YEAR', 'CUSTOM']).default('MONTH'),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
}).refine(
  (data) => {
    if (data.period === 'CUSTOM') {
      return data.startDate && data.endDate;
    }
    return true;
  },
  {
    message: 'Start date and end date are required for custom period',
    path: ['startDate'],
  }
).refine(
  (data) => {
    if (data.startDate && data.endDate) {
      return data.endDate >= data.startDate;
    }
    return true;
  },
  {
    message: 'End date must be after or equal to start date',
    path: ['endDate'],
  }
);

/**
 * Report Dashboard Schema
 */
export const reportDashboardSchema = z.object({
  widgets: z.array(z.enum(['SUMMARY', 'TRENDS', 'TOP_CUSTOMERS', 'RECENT_ACTIVITY', 'PERFORMANCE'])).optional(),
  period: z.enum(['TODAY', 'WEEK', 'MONTH', 'QUARTER', 'YEAR']).default('MONTH'),
  refreshInterval: z.coerce.number().int().min(30).max(3600).default(300), // 5 minutes default
});

/**
 * Type exports for use in API routes
 */
export type ReportType = z.infer<typeof reportTypeSchema>;
export type ReportFormat = z.infer<typeof reportFormatSchema>;
export type BaseReportFilter = z.infer<typeof baseReportFilterSchema>;
export type AMCReportFilter = z.infer<typeof amcReportFilterSchema>;
export type WarrantyReportFilter = z.infer<typeof warrantyReportFilterSchema>;
export type ServiceReportFilter = z.infer<typeof serviceReportFilterSchema>;
export type SalesReportFilter = z.infer<typeof salesReportFilterSchema>;
export type CustomerReportFilter = z.infer<typeof customerReportFilterSchema>;
export type FinancialReportFilter = z.infer<typeof financialReportFilterSchema>;
export type AnalyticsReportFilter = z.infer<typeof analyticsReportFilterSchema>;
export type ReportExport = z.infer<typeof reportExportSchema>;
export type ReportStatistics = z.infer<typeof reportStatisticsSchema>;
export type ReportDashboard = z.infer<typeof reportDashboardSchema>;
