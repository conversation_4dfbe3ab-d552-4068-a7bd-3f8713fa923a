'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { SearchableCustomerSelect } from '@/components/ui/searchable-customer-select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Search, RotateCcw } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface ReportParametersFormProps {
  reportType: string;
  availableFilters: string[];
  sortOptions: Array<{ value: string; label: string }>;
  onSubmit: (filters: Record<string, any>) => void;
  isLoading?: boolean;
}

// Create dynamic schema based on available filters
const createParameterSchema = (availableFilters: string[]) => {
  const schemaFields: Record<string, z.ZodTypeAny> = {};

  availableFilters.forEach(filter => {
    switch (filter) {
      case 'startDate':
      case 'endDate':
        schemaFields[filter] = z.date().optional();
        break;
      case 'customerId':
      case 'executiveId':
      case 'technicianId':
        schemaFields[filter] = z.string().optional();
        break;
      case 'amountMin':
      case 'amountMax':
        schemaFields[filter] = z.coerce.number().min(0).optional();
        break;
      case 'page':
      case 'limit':
        schemaFields[filter] = z.coerce.number().int().min(1).optional();
        break;
      case 'isActive':
        schemaFields[filter] = z.boolean().optional();
        break;
      default:
        schemaFields[filter] = z.string().optional();
    }
  });

  // Always include pagination and sorting
  schemaFields.page = z.coerce.number().int().min(1).optional();
  schemaFields.limit = z.coerce.number().int().min(1).max(1000).optional();
  schemaFields.sortBy = z.string().optional();
  schemaFields.sortOrder = z.enum(['asc', 'desc']).optional();

  return z.object(schemaFields);
};

/**
 * Report Parameters Form Component
 * 
 * Dynamically generates form fields based on the available filters for a report type.
 * Handles various input types including dates, dropdowns, and search fields.
 */
export function ReportParametersForm({
  reportType,
  availableFilters,
  sortOptions,
  onSubmit,
  isLoading = false,
}: ReportParametersFormProps) {
  const [executives, setExecutives] = useState<Array<{ id: string; name: string }>>([]);
  const [statusOptions, setStatusOptions] = useState<Record<string, string[]>>({});

  const schema = createParameterSchema(availableFilters);
  type FormData = z.infer<typeof schema>;

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      page: 1,
      limit: 50,
      sortOrder: 'desc',
    },
  });

  // Watch form values for controlled components
  const watchedValues = watch();

  // Fetch reference data
  useEffect(() => {
    const fetchReferenceData = async () => {
      try {
        // Fetch executives if needed
        if (availableFilters.includes('executiveId') || availableFilters.includes('technicianId')) {
          const response = await fetch('/api/users?role=EXECUTIVE', {
            credentials: 'include',
          });
          if (response.ok) {
            const data = await response.json();
            setExecutives(data.users || []);
          }
        }

        // Set status options based on report type
        const statusMap: Record<string, string[]> = {
          AMC: ['ACTIVE', 'EXPIRED', 'PENDING', 'CANCELLED'],
          WARRANTY: ['ACTIVE', 'EXPIRED', 'PENDING'],
          SERVICE: ['OPEN', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'],
          SALES: ['DRAFT', 'PENDING', 'CONFIRMED', 'DELIVERED', 'CANCELLED'],
          CUSTOMER: ['ACTIVE', 'INACTIVE'],
        };
        
        setStatusOptions(statusMap);
      } catch (error) {
        console.error('Error fetching reference data:', error);
      }
    };

    fetchReferenceData();
  }, [availableFilters, reportType]);

  const handleFormSubmit = (data: FormData) => {
    // Convert dates to ISO strings and handle "all" values
    const processedData = { ...data };
    Object.keys(processedData).forEach(key => {
      if (processedData[key] instanceof Date) {
        processedData[key] = processedData[key].toISOString();
      } else if (processedData[key] === 'all') {
        // Convert "all" back to undefined/empty for API
        processedData[key] = undefined;
      }
    });

    onSubmit(processedData);
  };

  const handleReset = () => {
    reset({
      page: 1,
      limit: 50,
      sortOrder: 'desc',
    });
  };

  const renderField = (filterName: string) => {
    const fieldId = `field-${filterName}`;
    
    switch (filterName) {
      case 'startDate':
      case 'endDate':
        return (
          <div key={filterName} className="space-y-2">
            <Label htmlFor={fieldId}>
              {filterName === 'startDate' ? 'Start Date' : 'End Date'}
            </Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id={fieldId}
                  variant="outline"
                  className={cn(
                    'w-full justify-start text-left font-normal',
                    !watchedValues[filterName] && 'text-muted-foreground'
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {watchedValues[filterName] ? (
                    format(watchedValues[filterName] as Date, 'PPP')
                  ) : (
                    <span>Pick a date</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={watchedValues[filterName] as Date}
                  onSelect={(date) => setValue(filterName, date)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        );

      case 'customerId':
        return (
          <div key={filterName} className="space-y-2">
            <Label htmlFor={fieldId}>Customer</Label>
            <SearchableCustomerSelect
              value={watchedValues[filterName] as string}
              onValueChange={(value) => setValue(filterName, value)}
              placeholder="Select customer..."
            />
          </div>
        );

      case 'executiveId':
      case 'technicianId':
        return (
          <div key={filterName} className="space-y-2">
            <Label htmlFor={fieldId}>
              {filterName === 'executiveId' ? 'Executive' : 'Technician'}
            </Label>
            <Select
              value={watchedValues[filterName] as string}
              onValueChange={(value) => setValue(filterName, value)}
            >
              <SelectTrigger id={fieldId}>
                <SelectValue placeholder={`Select ${filterName === 'executiveId' ? 'executive' : 'technician'}...`} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                {executives.map((executive) => (
                  <SelectItem key={executive.id} value={executive.id}>
                    {executive.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        );

      case 'amcStatus':
      case 'warrantyStatus':
      case 'serviceStatus':
      case 'salesStatus':
        const statusKey = reportType as keyof typeof statusOptions;
        const options = statusOptions[statusKey] || [];
        
        return (
          <div key={filterName} className="space-y-2">
            <Label htmlFor={fieldId}>Status</Label>
            <Select
              value={watchedValues[filterName] as string}
              onValueChange={(value) => setValue(filterName, value)}
            >
              <SelectTrigger id={fieldId}>
                <SelectValue placeholder="Select status..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                {options.map((status) => (
                  <SelectItem key={status} value={status}>
                    {status.replace('_', ' ')}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        );

      case 'amountMin':
      case 'amountMax':
        return (
          <div key={filterName} className="space-y-2">
            <Label htmlFor={fieldId}>
              {filterName === 'amountMin' ? 'Minimum Amount' : 'Maximum Amount'}
            </Label>
            <Input
              id={fieldId}
              type="number"
              min="0"
              step="0.01"
              placeholder="0.00"
              {...register(filterName)}
            />
            {errors[filterName] && (
              <p className="text-sm text-red-600">{errors[filterName]?.message}</p>
            )}
          </div>
        );

      case 'search':
        return (
          <div key={filterName} className="space-y-2">
            <Label htmlFor={fieldId}>Search</Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id={fieldId}
                placeholder="Search..."
                className="pl-10"
                {...register(filterName)}
              />
            </div>
          </div>
        );

      case 'limit':
        return (
          <div key={filterName} className="space-y-2">
            <Label htmlFor={fieldId}>Records per Page</Label>
            <Select
              value={String(watchedValues[filterName] || 50)}
              onValueChange={(value) => setValue(filterName, parseInt(value))}
            >
              <SelectTrigger id={fieldId}>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
                <SelectItem value="250">250</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );

      default:
        // Generic text input for other filters
        return (
          <div key={filterName} className="space-y-2">
            <Label htmlFor={fieldId}>
              {filterName.charAt(0).toUpperCase() + filterName.slice(1).replace(/([A-Z])/g, ' $1')}
            </Label>
            <Input
              id={fieldId}
              placeholder={`Enter ${filterName}...`}
              {...register(filterName)}
            />
          </div>
        );
    }
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {/* Filter Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {availableFilters
          .filter(filter => !['page', 'sortBy', 'sortOrder'].includes(filter))
          .map(renderField)}
      </div>

      {/* Sorting and Pagination */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t">
        <div className="space-y-2">
          <Label htmlFor="sortBy">Sort By</Label>
          <Select
            value={watchedValues.sortBy as string}
            onValueChange={(value) => setValue('sortBy', value)}
          >
            <SelectTrigger id="sortBy">
              <SelectValue placeholder="Select sort field..." />
            </SelectTrigger>
            <SelectContent>
              {sortOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="sortOrder">Sort Order</Label>
          <Select
            value={watchedValues.sortOrder as string}
            onValueChange={(value) => setValue('sortOrder', value as 'asc' | 'desc')}
          >
            <SelectTrigger id="sortOrder">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="asc">Ascending</SelectItem>
              <SelectItem value="desc">Descending</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {renderField('limit')}
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between pt-4 border-t">
        <Button
          type="button"
          variant="outline"
          onClick={handleReset}
          disabled={isLoading}
        >
          <RotateCcw className="h-4 w-4 mr-2" />
          Reset
        </Button>

        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              Generating...
            </>
          ) : (
            <>
              <Search className="h-4 w-4 mr-2" />
              Generate Report
            </>
          )}
        </Button>
      </div>
    </form>
  );
}
